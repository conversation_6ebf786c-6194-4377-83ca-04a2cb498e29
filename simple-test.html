<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
</head>
<body>
    <h1>Simple JavaScript Test</h1>
    <button onclick="testFunction()">Test Function</button>
    <div id="result"></div>
    
    <script>
        console.log('Inline script loaded');
        
        function testFunction() {
            document.getElementById('result').innerHTML = 'Inline JavaScript works!';
        }
        
        // Test if external scripts load
        window.addEventListener('load', function() {
            console.log('Window loaded');
            console.log('apiClient:', typeof window.apiClient);
            console.log('openLoginModal:', typeof window.openLoginModal);
        });
    </script>
    
    <script src="api-config.js"></script>
    <script src="script.js"></script>
    
    <script>
        console.log('After external scripts');
        console.log('apiClient after load:', typeof window.apiClient);
        console.log('openLoginModal after load:', typeof window.openLoginModal);
    </script>
</body>
</html>
