<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug JavaScript</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>JavaScript Debug Page</h1>
    
    <div class="test-section">
        <h2>1. Basic JavaScript Test</h2>
        <button onclick="testBasicJS()">Test Basic JS</button>
        <div id="basic-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Script Loading Test</h2>
        <button onclick="testScriptLoading()">Test Script Loading</button>
        <div id="script-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Global Functions Test</h2>
        <button onclick="testGlobalFunctions()">Test Global Functions</button>
        <div id="global-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. DOM Manipulation Test</h2>
        <button onclick="testDOMManipulation()">Test DOM</button>
        <div id="dom-result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. Event Listener Test</h2>
        <button id="event-test-btn">Click Me (Event Listener)</button>
        <div id="event-result"></div>
    </div>

    <!-- Load the actual scripts -->
    <script src="api-config.js"></script>
    <script src="script.js"></script>
    
    <script>
        // Test functions
        function testBasicJS() {
            const result = document.getElementById('basic-result');
            try {
                const test = 2 + 2;
                result.innerHTML = `<span class="success">✓ Basic JavaScript works: 2 + 2 = ${test}</span>`;
            } catch (error) {
                result.innerHTML = `<span class="error">✗ Basic JavaScript failed: ${error.message}</span>`;
            }
        }
        
        function testScriptLoading() {
            const result = document.getElementById('script-result');
            const tests = [];
            
            // Check if api-config.js loaded
            if (typeof window.apiClient !== 'undefined') {
                tests.push('<span class="success">✓ api-config.js loaded (apiClient exists)</span>');
            } else {
                tests.push('<span class="error">✗ api-config.js not loaded (apiClient missing)</span>');
            }
            
            // Check if script.js loaded (look for specific functions)
            if (typeof window.openLoginModal !== 'undefined') {
                tests.push('<span class="success">✓ script.js loaded (openLoginModal exists)</span>');
            } else {
                tests.push('<span class="error">✗ script.js not loaded (openLoginModal missing)</span>');
            }
            
            result.innerHTML = tests.join('<br>');
        }
        
        function testGlobalFunctions() {
            const result = document.getElementById('global-result');
            const tests = [];
            
            const functionsToTest = [
                'openLoginModal',
                'openSignupModal', 
                'closeAuthModal',
                'addToCart',
                'toggleWishlist',
                'togglePasswordVisibility'
            ];
            
            functionsToTest.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    tests.push(`<span class="success">✓ ${funcName} function available</span>`);
                } else {
                    tests.push(`<span class="error">✗ ${funcName} function missing</span>`);
                }
            });
            
            result.innerHTML = tests.join('<br>');
        }
        
        function testDOMManipulation() {
            const result = document.getElementById('dom-result');
            try {
                // Create a test element
                const testDiv = document.createElement('div');
                testDiv.id = 'test-element';
                testDiv.textContent = 'Test Element';
                document.body.appendChild(testDiv);
                
                // Try to find it
                const found = document.getElementById('test-element');
                if (found) {
                    result.innerHTML = '<span class="success">✓ DOM manipulation works</span>';
                    document.body.removeChild(testDiv);
                } else {
                    result.innerHTML = '<span class="error">✗ DOM manipulation failed</span>';
                }
            } catch (error) {
                result.innerHTML = `<span class="error">✗ DOM manipulation error: ${error.message}</span>`;
            }
        }
        
        // Test event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const eventBtn = document.getElementById('event-test-btn');
            const eventResult = document.getElementById('event-result');
            
            if (eventBtn && eventResult) {
                eventBtn.addEventListener('click', function() {
                    eventResult.innerHTML = '<span class="success">✓ Event listeners work</span>';
                });
            }
            
            // Auto-run tests after a short delay
            setTimeout(() => {
                testBasicJS();
                testScriptLoading();
                testGlobalFunctions();
                testDOMManipulation();
            }, 500);
        });
        
        // Console logging for debugging
        console.log('Debug script loaded');
        console.log('window.apiClient:', window.apiClient);
        console.log('window.openLoginModal:', window.openLoginModal);
    </script>
</body>
</html>
