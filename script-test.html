<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Script Loading Test</title>
</head>
<body>
    <h1>Script Loading Test</h1>
    <div id="status"></div>
    <button onclick="testLoginModal()">Test Login Modal</button>
    <button onclick="testAddToCart()">Test Add to Cart</button>
    
    <script>
        console.log('Starting script loading test...');
        
        function updateStatus(message) {
            const status = document.getElementById('status');
            status.innerHTML += '<div>' + message + '</div>';
            console.log(message);
        }
        
        function testLoginModal() {
            if (typeof window.openLoginModal === 'function') {
                updateStatus('✓ openLoginModal function is available');
                try {
                    window.openLoginModal();
                    updateStatus('✓ openLoginModal executed successfully');
                } catch (error) {
                    updateStatus('✗ openLoginModal error: ' + error.message);
                }
            } else {
                updateStatus('✗ openLoginModal function not found');
            }
        }
        
        function testAddToCart() {
            if (typeof window.addToCart === 'function') {
                updateStatus('✓ addToCart function is available');
                try {
                    window.addToCart(1, 'Test Product', 29.99);
                    updateStatus('✓ addToCart executed successfully');
                } catch (error) {
                    updateStatus('✗ addToCart error: ' + error.message);
                }
            } else {
                updateStatus('✗ addToCart function not found');
            }
        }
        
        // Check script loading
        updateStatus('Checking script loading...');
        
        // Test 1: Check if scripts are loaded
        setTimeout(() => {
            updateStatus('Checking after 1 second...');
            updateStatus('apiClient type: ' + typeof window.apiClient);
            updateStatus('openLoginModal type: ' + typeof window.openLoginModal);
            updateStatus('addToCart type: ' + typeof window.addToCart);
        }, 1000);
    </script>
    
    <!-- Try loading scripts with explicit error handling -->
    <script src="/api-config.js" onerror="updateStatus('✗ Failed to load api-config.js')" onload="updateStatus('✓ api-config.js loaded')"></script>
    <script src="/script.js" onerror="updateStatus('✗ Failed to load script.js')" onload="updateStatus('✓ script.js loaded')"></script>
    
    <script>
        // Final check after all scripts should be loaded
        setTimeout(() => {
            updateStatus('Final check after 2 seconds...');
            updateStatus('apiClient final: ' + typeof window.apiClient);
            updateStatus('openLoginModal final: ' + typeof window.openLoginModal);
            updateStatus('addToCart final: ' + typeof window.addToCart);
        }, 2000);
    </script>
</body>
</html>
