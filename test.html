<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-4">JavaScript Test</h1>
        
        <!-- Test basic button click -->
        <button id="test-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mb-4">
            Click Me
        </button>
        <div id="test-result" class="mb-4 p-2 bg-gray-100 rounded"></div>
        
        <!-- Test login modal -->
        <button onclick="openLoginModal()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mb-4">
            Open Login Modal
        </button>
        
        <!-- Test cart functionality -->
        <button onclick="addToCart(1, 'Test Product', 29.99)" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 mb-4">
            Add to Cart
        </button>
        
        <!-- Cart display -->
        <div class="border p-4 rounded">
            <h3 class="font-bold">Cart Items: <span id="cart-count">0</span></h3>
            <div id="cart-items"></div>
        </div>
    </div>

    <!-- Simple Login Modal -->
    <div id="login-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center">
        <div class="bg-white p-6 rounded-lg">
            <h2 class="text-xl font-bold mb-4">Login</h2>
            <button onclick="closeAuthModal()" class="bg-red-500 text-white px-4 py-2 rounded">Close</button>
        </div>
    </div>

    <!-- Load JavaScript files -->
    <script src="api-config.js"></script>
    <script src="script.js"></script>
    
    <!-- Test script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            
            // Test basic functionality
            const testBtn = document.getElementById('test-btn');
            const testResult = document.getElementById('test-result');
            
            if (testBtn && testResult) {
                testBtn.addEventListener('click', function() {
                    testResult.textContent = 'Button clicked! JavaScript is working.';
                    testResult.className = 'mb-4 p-2 bg-green-100 text-green-800 rounded';
                });
            }
            
            // Test if global functions are available
            setTimeout(() => {
                const results = [];
                
                if (typeof openLoginModal === 'function') {
                    results.push('✓ openLoginModal function available');
                } else {
                    results.push('✗ openLoginModal function NOT available');
                }
                
                if (typeof addToCart === 'function') {
                    results.push('✓ addToCart function available');
                } else {
                    results.push('✗ addToCart function NOT available');
                }
                
                if (typeof window.apiClient === 'object') {
                    results.push('✓ apiClient object available');
                } else {
                    results.push('✗ apiClient object NOT available');
                }
                
                if (testResult) {
                    testResult.innerHTML = results.join('<br>');
                }
            }, 1000);
        });
    </script>
</body>
</html>
